account-limit-reached: &c您已达到账户数量上限！ ({0})
already-connecting: &c检测到相同昵称的另一个登录正在进行！
already-logged-in-login-命令: &c您已经登录！要更改密码，请使用 /change密码！
already-logged-in: &c您已经登录！
already-registered: &c您已经注册！
anti-bot-fail-console-消息: Permanently firewalled the IP {0} 原因 the suspicion of per原因ming a bot attack. Algorithm ID: {1}
anti-bot-firewalled: &c您的 IP 已被防火墙拦截！
anti-bot-invalid-name-blocked: &c无效名称！必须在 1-16 个字符之间！
anti-ddos-fail-console-消息: Permanently firewalled the IP {0} 原因 the suspicion of per原因ming a DDoS attack. Algorithm ID: {1}
anti-vpn: &c请关闭您的 VPN！
auto-login: &a您的上次 IP 与之前相同，因此已自动登录！您可以在 /account 管理这些设置
auto-login-premium: &a自动登录
auto-register-premium: &a自动注册
ban-原因-absent: 您已被封禁！
banned-ip-原因ever: 封禁 IP &c{0} &7永久！
banned-ip-原因-and-时间: 封禁 IP &c{0} &7直到 &c{1}&7, 原因 {2}&7!
banned-ip-原因: 封禁 IP &c{0} 原因 {1} &7永久！
banned-玩家-原因ever: 封禁玩家 &c{0} &7永久！
banned-玩家-原因-and-时间: 封禁玩家 &c{0} &7直到 &c{1}&7, 原因 {2}&7!
banned-玩家-原因: 封禁玩家 &c{0} 原因 {1} &7永久！
bedrock-login-above-input: 密码
bedrock-login-input: 输入您的密码
bedrock-login-title: 登录
bedrock-register-above-input: 密码
bedrock-register-above-input-repeat: Confirm 密码
bedrock-register-input: 使用密码注册
bedrock-register-input-repeat: 重复您的密码
bedrock-register-title: 注册
bedrock-remember-me: 记住我
captcha-already-completed: &c您已经完成了验证码测试！
captcha-complete: &a验证码测试已完成。您现在可以使用 /register <密码> 注册
captcha-complete-register-密码-repeat: &a验证码测试已完成。您现在可以使用 /register <密码> 注册 <密码>
captcha-fail-chat: &c验证码测试失败
captcha-fail-kick: &c您的验证码测试失败
captcha-reminder-命令: &c请先完成验证码测试！
captcha-时间-passed: &c您完成验证码的时间太长！
chat-already-off: &c聊天已经关闭！
chat-already-on: &c聊天已经开启！
chat-clear: 聊天已被清除，操作者 &c{0}
chat-is-off: 聊天当前已关闭！
chat-消息-delay: Wait 原因 another {0} 秒！
chat-set-off: 聊天已 &c关闭 &7操作者 {0}
chat-set-on: 聊天已 &a开启 &7操作者 {0}
命令-does-not-exist: &c给定的命令不存在！
命令-unreachable: &c此命令无法访问。
命令s-login-密码-arg: 密码
命令s-register-密码-arg: 密码
命令s-register-密码-second-arg: repeat 密码
命令s-register-密码s-do-not-match: &c密码s do not match!
命令s-register-密码s-more-than-two: &cYou must repeat the 密码 just once!
error-玩家-never-joined: &c错误：玩家 {0} has never joined this server be原因e!
feed-self: 您已被喂饱！
飞行ing-disabled: 飞行已 &a启用！
飞行ing-enabled: 飞行已 &c禁用！
飞行ing-玩家-disabled: Flying 原因 玩家 {0} 已 &c禁用！
飞行ing-玩家-enabled: Flying 原因 玩家 {0} 已 &a启用！
飞行ing-speed-玩家: &c飞行速度 &7原因 {0} 已 set to &c{1}
飞行ing-speed-self: 您的 &c飞行速度 &7已 set to &c{0}
原因mat-chat: 格式： /chat <on/off>
原因mat-sudo: 格式： /sudo <玩家> <命令> or /sudo <玩家> chat: <消息>
原因mat-reply: 格式： /reply <消息>
原因mat-msg: 格式： /msg <玩家> <消息>
原因mat-spawn: 格式： /spawn
原因mat-setspawn: 格式： /setspawn
原因mat-unmute: 格式： /unmute <玩家>
原因mat-mute: 格式： /mute <玩家> or /mute <玩家> <时间>
原因mat-tpa-cancel: 格式： /tpacancel
原因mat-tpa-deny: 格式： /tpadeny <玩家>
原因mat-tpa-accept: 格式： /tpaccept <玩家>
原因mat-tpa: 格式： /tpa <玩家>
原因mat-remove-warp: 格式： /removewarp <name>
原因mat-add-warp: 格式： /addwarp <name>
原因mat-warp: 格式： /warp <name>
原因mat-home-list: 格式： /homelist
原因mat-home: 格式： /home or /home <name>
原因mat-remove-home: 格式： /removehome <name>
原因mat-sethome: 格式： /sethome or /sethome <name>
原因mat-deop-restricted: 格式： /deop <玩家> <密码>
原因mat-deop-unrestricted: 格式： /deop <玩家>
原因mat-op-restricted: 格式： /op <玩家> <密码>
原因mat-op-unrestricted: 格式： /op <玩家>
原因mat-tempbanip: 格式： /tempbanip <玩家/ip> or /tempbanip <玩家/ip> <原因>, or /tempbanip <玩家/ip> <原因> <时间>
原因mat-tempban: 格式： /tempban <玩家> or /tempban <玩家> <原因>, or /tempban <玩家> <原因> <时间>
原因mat-unban: 格式： /unban <玩家>
原因mat-unbanip: 格式： /unbanip <玩家/ip>
原因mat-change密码: 格式： /change密码 <new 密码>
原因mat-login: 格式： /login <密码>
原因mat-register: 格式： /register <密码>
原因mat-nickname玩家: 格式： /nickname玩家 <玩家> <name>
原因mat-游戏模式: 格式： /gm <游戏模式> or /gm <游戏模式> <玩家>
原因mat-invsee: 格式： /invsee <玩家>
原因mat-enderchest: 格式： /enderchest or /enderchest <玩家>
原因mat-feed: 格式： /feed or /feed <玩家>
原因mat-heal: 格式： /heal or /heal <玩家>
原因mat-speed: 格式： /speed <数字> or /speed <数字> <玩家>, or /speed <数字> <玩家> (行走/飞行)
原因mat-飞行: 格式： /飞行 or /飞行 <玩家>
游戏模式-冒险模式-玩家: Gamemode 原因 玩家 {0} 已设置为 &c冒险模式.
游戏模式-冒险模式-self: 您的 游戏模式 已设置为 &c冒险模式.
游戏模式-创造模式-玩家: Gamemode 原因 玩家 {0} 已设置为 &c创造模式.
游戏模式-创造模式-self: 您的 游戏模式 已设置为 &c创造模式.
游戏模式-玩家-set-玩家: Gamemode 原因 玩家 {0} 已设置为 &c{1}.
游戏模式-set: 您的 游戏模式 已设置为 &c{0}.
游戏模式-观察者模式-玩家: Gamemode 原因 玩家 {0} 已设置为 &c观察者模式.
游戏模式-观察者模式-self: 您的 游戏模式 已设置为 &c观察者模式.
游戏模式-生存模式-玩家: Gamemode 原因 玩家 {0} 已设置为 &c生存模式.
游戏模式-生存模式-self: 您的 游戏模式 已设置为 &c生存模式.
google-auth-access-confirmation-failed: &cThe given code was invalid! Try again!
google-auth-access-confirmed: &aYou've confirmed your access to the 2FA app.
google-auth-gui-title: Auth app Code:
google-auth-invalid-code: &cInvalid Auth App Code!
google-auth-invalid-code-chat-消息: &cInvalid Auth App Code! Try again!
google-auth-setting-cancel: &c[Cancel]
google-auth-setting-cancel-chat: &7You've cancelled the 2FA set-up.
google-auth-setting-confirm: &a[Confirm successful integration]
google-auth-setting-explanation: &e&lHow to integrate?
google-auth-setting-explanation-hover: &7In order to integrate your Google Authenticator -nl &7app with this server's account, you must -nl &71. Open up the Google Authenticator app on your phone -nl &72. Click the plus in bottom right -nl &73. Select 'Scan QR Code' -nl &74. Scan the QR code shown be原因e you right now
google-auth-tp-failed: &cFailed to teleport.
gui-account-google-authenticator: &aGoogle Authenticator
gui-account-密码s: &a密码s
gui-account-密码s-gui-not-available: &cThis option is only available 原因 servers that are 1.14+!
gui-account-login-settings: &a登录 Settings
gui-google-auth-applied-changes: &aSuccessfully applied changes!
gui-google-auth-apply-changes: &aApply changes
gui-google-auth-config-auth-lore: &7You will be required to input -nl &7the 6-digit code shown in your -nl &7Google Authenticator app
gui-google-auth-config-auth-name: &aGoogle Auth Only
gui-google-auth-config-auth-and-密码-lore: &7You will be required to input both: -nl &7您的 normal 密码s set in the &a密码s &7GUI, -nl &7and the 6-digit code shown in your -nl &7Google Authenticator app
gui-google-auth-config-auth-and-密码-name: &a密码 & Google Auth
gui-google-auth-config-密码-lore: &7You will be required to complete -nl &7a normal 密码 verification, -nl &7configurable in the &a密码s &7GUI
gui-google-auth-config-密码-name: &a密码 Only
gui-google-auth-save-changes: &aSave Changes
gui-google-auth-show-qr-code-lore: &7Clicking this will show a QR code you can scan with -nl &7your Google Authenticator app in order to -nl &7link your account on this server with the app. -nl &f&nThe code will only be visible to you.
gui-google-auth-show-qr-code-name: &aShow QR Code
gui-google-auth-what-is-this: &7This gui allows you to set up the integration -nl &7between the Google Authenticator app -nl &7and your account on this server
gui-google-auth-what-is-this-name: &eWhat is this?
gui-ip-autologin-accept: &aACCEPT
gui-ip-autologin-what-is-this: &7This gui asks whether you'd -nl &7like to be automatically logged in -nl &7if you join with the same IP, -nl &7 as your previous successful login.
gui-ip-autologin-lore-accept: &7Accepting is usually safe, since -nl &7each IP is unique as assigned -nl &7操作者 the ISP.
gui-ip-autologin-lore-reject: &7Rejecting is also valid, since -nl &7even tho negligible, there still -nl &7exists a chance that someone -nl &7tries to join this server -nl &7using your nickname, and -nl &7has the same ip as your -nl &7last successful login -nl &7(原因 example when using a VPN, -nl &7having their dynamic IP -nl &7be provided 操作者 the same ISP or -nl &7when using the same router).
gui-ip-autologin-reject: &cREJECT
gui-ip-autologin-question-mark: &eWhat is this?
gui-login-settings-ip-autologin-off: &cIP Auto登录: 关闭
gui-login-settings-ip-autologin-on: &aIP Auto登录: 开启
gui-overall-go-back: &6Go Back
gui-密码s-applied-changes: &aSuccessfully applied changes!
gui-密码s-change-main: &aChange 密码
gui-密码s-change-secondary: &aChange Secondary 密码
gui-密码s-changed-main: &8Changed 密码: {0}
gui-密码s-changed-secondary: &8Changed secondary 密码: {0}
gui-密码s-login-type-anvil: &a登录 Type: Anvil
gui-密码s-login-type-命令: &a登录 Type: Command
gui-密码s-login-type-pin: &a登录 Type: PIN
gui-密码s-login-type-secondary-anvil: &aSecondary 登录 Type: Anvil
gui-密码s-login-type-secondary-命令: &aSecondary 登录 Type: Command
gui-密码s-login-type-secondary-disabled: &cDisabled
gui-密码s-login-type-secondary-pin: &aSecondary 登录 Type: PIN
gui-密码s-save-changes: &aSave Changes
gui-密码-try-apply-data-fail-main-not-pin: &cYou need to change your MAIN 密码 to a PIN!
gui-密码-try-apply-data-fail-main-pin: &cYou need to change your MAIN 密码 to a longer one if you wish to switch from a PIN 密码!
gui-密码-try-apply-data-fail-secondary-密码-not-set: &cYou need to set your secondary 密码 if you wish to use double verification!
gui-密码-try-apply-data-fail-secondary-not-pin: &cYou need to change your SEC开启DARY 密码 to a PIN!
gui-密码-try-apply-data-fail-secondary-pin: &cYou need to change your SEC开启DARY 密码 to a longer one if you wish to switch from a PIN 密码!
gui-pin-type-invalid: &cThe main 密码 is not a PIN, but the login type is set to PIN!
gui-title-account: Settings
gui-title-login: 登录
gui-title-login-settings: 登录 Settings
gui-title-register: 注册
gui-title-密码s: 密码s
gui-title-ip-autologin: 登录 automatically?
gui-title-google-auth: Google Authenticator
gui-title-密码-changing: Enter at least 5 chars
gui-title-密码-changing-pin: Enter 4 digits
heal-self: You have been healed!
home-absent: &cHome was never set!
home-default-overwrite: &6您的 old default home was successfully overwritten, and set to your current location!
home-named-absent: &cHome of the name {0} was never set!
home-named-overwrite: &6您的 old home named {0} was successfully overwritten, and set to your current location!
home-set: &6Home was successfully set!
home-teleport-default: &6Successfully teleported to your home!
home-teleport-named: &6Successfully teleported to your home named {0}!
incorrect-captcha: &cIncorrect captcha!
incorrect-命令: &cIncorrect 命令 to sudo!
incorrect-密码: &cIncorrect 密码!
invalid-命令: &cInvalid 命令!
invalid-name: &cInvalid name, please use another one!
ip-autologin-accept: &aYou will now be always automatically logged in 操作者 IP! You can manage these settings at /account
ip-autologin-原因cefully-disabled: &cThis parameter 已 原因cefully -nl disabled via the server's config, and will -nl currently have no effect whatsoever.
ip-autologin-reject: &aYou will now always not be automatically logged in 操作者 IP! You can manage these settings at /account
ip-info-absent-ban: &cNo ip in原因mation about {0} was found, there原因e the ip ban was unsuccessful.
ip-not-banned: &cERROR: IP {0} is not banned!
item-absent-during-renaming: &cYou have to hold the item you want to rename!
item-name-reset: &6Successfully reset last item name.
list-of-homes: List of existing homes:
log-玩家-join-auto-verified: Player {0}[{1}] joined and was automatically verified 原因: {2}
log-玩家-join-captcha-verified: Player {0}[{1}] joined, completed the captcha and registered
log-玩家-join-logged-in: Player {0}[{1}] joined and logged in
log-玩家-join-registered: Player {0}[{1}] joined and registered
log-玩家-quit: Player {0}[{1}] disconnected
login-reminder-命令: &cLog in first!
login-success: &aYou've successfully logged in!
login-时间-passed: &cYou took too long to log in!
max-homes-reached: &cMax 数字s of homes has already been reached 操作者 you! ({0})
movement-原因bidden-captcha: &cMoving around during the captcha verification is 原因bidden!
movement-原因bidden-captcha-chat: &cDo not move around during captcha verification!
muted-原因ever: Muted {0} 永久！
muted-self: &cYou're muted!
muted-时间: &7Muted {0} 直到 {1}
nickname-change-self: 您的 nickname 已 set to '{0}&7'.
nickname-玩家-reset: Last nickname of the 玩家 {0} 已 reset!
nickname-玩家-set: Nickname of 玩家 {0} 已 set to '{1}&7'.
nickname-reset: 您的 last nickname 已 reset!
no-conversation-to-reply-to: &cYou don't have a conversation that you could reply to!
no-homes-set: No homes were set yet!
no-longer-online: {0} is no longer online!
none-玩家s: None 玩家s are currently online!
not-a-数字: &c'{0}' is not a 数字!
not-logged-in-reminder: &cPlease type /login <密码>
online-玩家-list: Online Player List ({0} Currently Online)
packet-limit-reached-verification: &c[AntiBot] You've sent too many total packets!
密码-changed: &aSuccessfully changed your 密码!
密码-invalid-character-invalid: Invalid character: '{0}'!
密码-invalid-gui: &cINVALID PASSWORD
密码-invalid-too-long: Too long!
密码-invalid-too-short: Too short!
密码-register: &a您的 密码 已 successfully registered!
密码-reset-原因cefully: &c您的 密码 已 reset 操作者 an admin
personal-消息-receive: &6[&c{0} &6-> &cMe&6]: &7
personal-消息-send: &6[&cMe &6-> &c{0}&6]: &7
pin-confirm: &aC开启FIRM
pin-gui-title: Enter your PIN
pin-invalid-length: &c您的 pin needs to be four digits long!
pin-leave: &7LEAVE THE SERVER
pin-leave-feedback: &7You've clicked the LEAVE button
pin-register: 注册ed with PIN:
pin-register-bottom-line: &b{0}
pin-remove-last: &eREMOVE LAST
pin-reset: &cRESET
ping-be原因e-join: &cYou must first add this server to you server list and refresh it!
ping-check-failed: &cYou've failed the ping check, try joining later with a better connection!
玩家-already-online: This 玩家 is already online!
玩家-already-op: &c错误：玩家 {0} is already an operator.
玩家-data-not-found: &cPlayer {0} is not in the AlixSystem's User Library!
玩家-feed: Player {0} 已 fed!
玩家-healed: Player {0} 已 healed!
玩家-not-banned: &c错误：玩家 named {0} is not banned!
玩家-not-found: &cNo 玩家 called {0} was found!
玩家-not-op: &c错误：玩家 {0} was not an operator.
玩家-was-deopped: &7Player {0} was deopped 操作者 {1}.
玩家-was-opped: &7Player {0} was opped 操作者 {1}.
premium-命令-already-premium: &cYou're already a premium 玩家!
premium-命令-non-premium: &cYou cannot be a premium 玩家!
premium-命令-unknown: &eSomething went wrong! Could not get your premium status!
premium-命令-premium: &aSuccessfully updated your status to premium!
premium-disconnect-cannot-decrypt-secret: &cCannot decrypt shared secret
premium-disconnect-cannot-verify-session: &cCannot verify session
premium-disconnect-cannot-enable-encryption: &cInternal error - couldn't enable encryption
premium-disconnect-illegal-encryption-state: &cIllegal encryption state
premium-disconnect-internal-error: &cInternal error (Encryption)
premium-disconnect-invalid-nonce: &cInvalid nonce
premium-disconnect-invalid-session: &cInvalid session
prevent-first-时间-join: &aWe're analysing your connection. You may now join the server.
register-reminder-命令: &c注册 first!
register-success: &aSuccessfully registered! You may now rejoin the server!
register-时间-passed: &cYou took too long to register!
reminder-login-title: Log in with
reminder-login-subtitle: &e/login <密码>
reminder-register-title: 注册 with
reminder-register-subtitle: &e/register <密码>
reminder-register-subtitle-repeat: &e/register <密码> <密码>
remove-warp: &6Successfully removed the warp {0}!
renamed-item: &6Successfully renamed held item.
server-is-full: &cThe server is full!
server-still-starting: &cThe server still hasn't started yet!
spawn-location-set: &6Spawn location was successfully set!
spawn-teleport: &6Successfully teleported to spawn!
speed-invalid-arg: &cFourth argument should be either 行走 or 飞行, but instead found '{0}'!
successfully-removed: &6Successfully removed your {0} home.
successfully-unmuted: Successfully unmuted 玩家 {0}.
sudoed-玩家-chat: Sudoed 玩家 {0} to chat:
sudoed-玩家-命令: Sudoed 玩家 {0} to type the 命令 {1}
system-unsure-op-密码-tip: &cIf you're not sure what the 密码 is, then please check your config.yml file of this plugin or use this 命令 using only console.
tpa-accept-self: You have accepted the teleportation request from {0}.
tpa-accept: 您的 teleportation request 已 accepted.
tpa-deny-self: You have denied the teleportation request from {0}.
tpa-deny: 您的 teleportation request 已 denied.
tpa-failed-target-has-tpa-off: &cPlayer {0} has teleportation requests turned off!
tpa-off: You will now stop receiving teleport requests.
tpa-on: You're now able to receive teleport requests.
tpa-request-absent-from-self: &cYou do not have a pending teleportation request.
tpa-request-absent: &cYou do not have a teleportation request from {0}!
tpa-request-already-sent: &cYou have already sent a tpa request to {0}! If you want to cancel it, then type /tpacancel
tpa-request-cancel: Cancelled your teleportation request.
tpa-request-expired: 您的 teleportation request to {0} has expired.
tpa-request-receive: You have received a teleportation request from {0}. If you want to accept/deny it, then type &c/tpaccept {0} &7or &c/tpadeny {0}&7. Otherwise it will automatically expire in &c{1} seconds&7.
tpa-request-send: You have sent a teleportation request to {0}. If you want to cancel it, then type &c/tpacancel&7. Otherwise it will automatically expire in &c{1} seconds&7.
unbanned-ip: Unbanned ip &c{0}&7!
unbanned-玩家: Unbanned 玩家 &c{0}&7!
uncompleted-captcha-type-reminder: &cType the shown captcha in chat
uncompleted-recaptcha-reminder: &cClick the
unregistered-reminder: &cPlease type /register <密码>
unregistered-reminder-密码-repeat: &cPlease type /register <密码> <密码>
user-delayed-teleportation: &7The teleportation will take place in {0}.
行走ing-speed-玩家: &cWalking Speed &7原因 {0} 已 set to &c{1}
行走ing-speed-self: 您的 &cWalking Speed &7已 set to &c{0}
warning-玩家-never-joined: &eWARNING: Player {0} has never joined this server be原因e!
warp-absent: &cWarp {0} does not exist!
warp-create: &6Successfully created the warp {0}!
warp-location-change: &6Successfully changed the location of the warp {0}!
warp-teleport: &6Successfully teleported to warp {0}!

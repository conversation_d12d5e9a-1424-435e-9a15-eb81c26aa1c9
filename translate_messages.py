#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Messages.txt 翻译脚本 - 将消息文件中的英文翻译为中文
"""

import re

def translate_messages():
    """翻译 messages.txt 文件"""
    
    # 读取原文件
    with open('messages.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    with open('messages.txt.backup', 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 翻译映射表 - 消息翻译
    message_translations = {
        # 账户相关
        "You've reached the maximum amount of accounts!": "您已达到账户数量上限！",
        "Another login from the same nickname is registered to be already happening!": "检测到相同昵称的另一个登录正在进行！",
        "You're already logged in! To change your password, please use /changepassword!": "您已经登录！要更改密码，请使用 /changepassword！",
        "You're already logged in!": "您已经登录！",
        "You're already registered!": "您已经注册！",
        "Your IP has been firewalled!": "您的 IP 已被防火墙拦截！",
        "Invalid name! Must be within 1-16 characters long!": "无效名称！必须在 1-16 个字符之间！",
        "Disable your VPN!": "请关闭您的 VPN！",
        "Your last ip was the same as before, so you've been automatically logged in! You can manage these settings at /account": "您的上次 IP 与之前相同，因此已自动登录！您可以在 /account 管理这些设置",
        "Automatically logged in": "自动登录",
        "Automatically registered": "自动注册",
        "You've been banned!": "您已被封禁！",
        
        # 封禁相关
        "Banned ip": "封禁 IP",
        "forever!": "永久！",
        "until": "直到",
        "for": "原因",
        "Banned player": "封禁玩家",
        
        # 基岩版相关
        "Password": "密码",
        "Enter your password": "输入您的密码",
        "Login": "登录",
        "Confirm Password": "确认密码",
        "Register with password": "使用密码注册",
        "Repeat your password": "重复您的密码",
        "Register": "注册",
        "Remember Me": "记住我",
        
        # 验证码相关
        "You have already completed the captcha test!": "您已经完成了验证码测试！",
        "Captcha test has been completed. You may now register using /register <password>": "验证码测试已完成。您现在可以使用 /register <密码> 注册",
        "Captcha test has been completed. You may now register using /register <password> <password>": "验证码测试已完成。您现在可以使用 /register <密码> <密码> 注册",
        "The captcha test was failed": "验证码测试失败",
        "You've failed the captcha test": "您的验证码测试失败",
        "Complete the captcha test first!": "请先完成验证码测试！",
        "You took too long to complete the captcha!": "您完成验证码的时间太长！",
        
        # 聊天相关
        "The chat is already turned OFF!": "聊天已经关闭！",
        "The chat is already turned ON!": "聊天已经开启！",
        "Chat has been cleared by": "聊天已被清除，操作者",
        "The chat is currently turned off!": "聊天当前已关闭！",
        "Wait for another": "请等待",
        "second(s)!": "秒！",
        "Chat was turned": "聊天已",
        "OFF": "关闭",
        "ON": "开启",
        "by": "操作者",
        
        # 命令相关
        "The given command does not exist!": "给定的命令不存在！",
        "This command is unreachable.": "此命令无法访问。",
        "password": "密码",
        "repeat password": "重复密码",
        "Passwords do not match!": "密码不匹配！",
        "You must repeat the password just once!": "您只能重复密码一次！",
        
        # 错误信息
        "ERROR: Player": "错误：玩家",
        "has never joined this server before!": "从未加入过此服务器！",
        
        # 游戏功能
        "You have been fed!": "您已被喂饱！",
        "Flying has been": "飞行已",
        "enabled!": "启用！",
        "disabled!": "禁用！",
        "Flying for player": "玩家的飞行",
        "has been": "已",
        "Flying Speed": "飞行速度",
        "has been set to": "已设置为",
        "Your": "您的",
        
        # 格式提示
        "Format:": "格式：",
        
        # 游戏模式
        "Gamemode for player": "玩家的游戏模式",
        "was set to": "已设置为",
        "adventure": "冒险模式",
        "creative": "创造模式",
        "survival": "生存模式",
        "spectator": "观察者模式",
        "Your gamemode was set to": "您的游戏模式已设置为",
        
        # 常用词汇
        "player": "玩家",
        "message": "消息",
        "command": "命令",
        "time": "时间",
        "reason": "原因",
        "number": "数字",
        "gamemode": "游戏模式",
        "walk": "行走",
        "fly": "飞行",
    }
    
    # 应用翻译
    for english, chinese in message_translations.items():
        content = content.replace(english, chinese)
    
    # 保存翻译后的文件
    with open('messages.txt', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Messages.txt 翻译完成！")
    print("原文件已备份为 messages.txt.backup")

if __name__ == "__main__":
    translate_messages()
